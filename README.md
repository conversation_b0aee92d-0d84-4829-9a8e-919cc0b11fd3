# BAD (Be A DJ) 🎵

A music submission application that allows users to submit songs to a Spotify playlist through a secure web interface.

## Features

- 🔐 Password-protected submission form
- 🎵 Spotify integration for song search and playlist management
- 🌐 Multi-language support (German/English)
- 📊 Admin dashboard for managing submissions
- 🗄️ Supabase database integration
- 🔒 Secure environment variable configuration

## Quick Start

### 1. Environment Setup

**Option A: Interactive Setup**
```bash
node setup-env.js
```

**Option B: Manual Setup**
```bash
# Copy environment templates
cp bad-server/.env.example bad-server/.env
cp bad-frontend/.env.example bad-frontend/.env

# Edit the .env files with your actual values
```

### 2. Install Dependencies

```bash
# Backend
cd bad-server
npm install

# Frontend
cd bad-frontend
npm install
```

### 3. Run Locally

```bash
# Start backend (terminal 1)
cd bad-server
npm start

# Start frontend (terminal 2)
cd bad-frontend
npm start
```

## Required Services

### Spotify API
1. Create a Spotify app at [Spotify Developer Dashboard](https://developer.spotify.com/dashboard)
2. Get your Client ID and Client Secret
3. Create a playlist and get its ID
4. Set redirect URI to your backend URL + `/callback`

### Supabase Database
1. Create a project at [Supabase](https://supabase.com)
2. Get your project URL and anon key
3. The app will automatically create required tables

## Deployment

See [DEPLOYMENT.md](./DEPLOYMENT.md) for detailed deployment instructions for:
- Vercel (full-stack)
- Koyeb + Vercel (backend + frontend)
- Other hosting platforms

## Project Structure

```
BAD-public/
├── bad-frontend/          # React frontend application
│   ├── src/
│   │   └── App.js        # Main application component
│   ├── .env.example      # Frontend environment template
│   └── package.json
├── bad-server/           # Node.js backend server
│   ├── server.js         # Main server file
│   ├── .env.example      # Backend environment template
│   ├── koyeb.toml        # Koyeb deployment config
│   └── package.json
├── vercel.json           # Vercel deployment config
├── setup-env.js          # Environment setup helper
└── DEPLOYMENT.md         # Detailed deployment guide
```

## Security

- All sensitive data is stored in environment variables
- No hardcoded passwords or API keys in the code
- Supabase Row Level Security (RLS) for data protection
- Secure password authentication for admin access

## Environment Variables

### Backend (.env)
- `SPOTIFY_CLIENT_ID` - Spotify app client ID
- `SPOTIFY_CLIENT_SECRET` - Spotify app client secret
- `SPOTIFY_PLAYLIST_ID` - Target playlist ID
- `SPOTIFY_REDIRECT_URI` - OAuth callback URL
- `SUPABASE_URL` - Supabase project URL
- `SUPABASE_ANON_KEY` - Supabase anonymous key
- `ADMIN_PASSWORD` - Admin panel password
- `SPOTIFY_AUTH_PASSWORD` - Spotify authentication password

### Frontend (.env)
- `REACT_APP_API_URL` - Backend API URL
- `REACT_APP_LOGIN_PASSWORD` - User login password

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test locally with environment variables
5. Submit a pull request

## License

This project is private and not licensed for public use.
