{"version": 2, "builds": [{"src": "bad-frontend/package.json", "use": "@vercel/static-build", "config": {"distDir": "build"}}, {"src": "bad-server/server.js", "use": "@vercel/node"}], "routes": [{"src": "/api/(.*)", "dest": "/bad-server/server.js"}, {"src": "/(.*)", "dest": "/bad-frontend/$1"}], "env": {"SPOTIFY_CLIENT_ID": "@spotify-client-id", "SPOTIFY_CLIENT_SECRET": "@spotify-client-secret", "SPOTIFY_PLAYLIST_ID": "@spotify-playlist-id", "SPOTIFY_REDIRECT_URI": "@spotify-redirect-uri", "SUPABASE_URL": "@supabase-url", "SUPABASE_ANON_KEY": "@supabase-anon-key", "ADMIN_PASSWORD": "@admin-password", "SPOTIFY_AUTH_PASSWORD": "@spotify-auth-password"}, "build": {"env": {"REACT_APP_API_URL": "@react-app-api-url", "REACT_APP_LOGIN_PASSWORD": "@react-app-login-password"}}}