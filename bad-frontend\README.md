# Be A DJ (BAD)
Be A DJ (BAD) ist eine Website, die es Nutzern ermöglicht, Songs für eine gemeinsame Playlist einzureichen. <PERSON>hn<PERSON> wie bei andere<PERSON> "DJ"-Plattformen können Sie hier Songs vorschlagen, die dann in eine offizielle Playlist verschoben werden.
So funktioniert es
BAD ist in zwei passwortgeschützte Bereiche unterteilt:
 * Submit-Seite: Hier können Benutzer Links von Spotify einreichen. Sie geben zusätzlich ihren Namen an, der dem Admin angezeigt wird.
 * Admin-Seite: Ein Admin kann hier die eingereichten Songs überprüfen und per Knopfdruck automatisch zur Playlist hinzufügen.
## Weitere Funktionen
 * Blockierliste: Unerwünschte Links können auf eine Blockierliste gesetzt werden. Das System lehnt diese dann automatisch ab.

## BAD nutzen
Es ist momentan noch nicht möglich als Privatperson zu <PERSON>, da zum einen der Code noch nicht öffentlich ist, und da das Backend und die Seiten Selbst gehosted werden müssen
