# BAD (Be A DJ) - Deployment Guide

This guide explains how to deploy the BAD application using environment variables instead of hardcoded values.

## Environment Variables

### Backend (bad-server)

Required environment variables for the backend:

```bash
# Spotify API Configuration
SPOTIFY_CLIENT_ID=your_spotify_client_id_here
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret_here
SPOTIFY_PLAYLIST_ID=your_spotify_playlist_id_here
SPOTIFY_REDIRECT_URI=https://your-backend-domain.com/callback

# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Authentication
ADMIN_PASSWORD=your_secure_admin_password_here
SPOTIFY_AUTH_PASSWORD=your_secure_spotify_auth_password_here

# Server Configuration (optional)
PORT=3000
```

### Frontend (bad-frontend)

Required environment variables for the frontend:

```bash
# API Configuration
REACT_APP_API_URL=https://your-backend-domain.com
REACT_APP_LOGIN_PASSWORD=your_secure_login_password_here
```

## Deployment Options

### Option 1: Vercel Deployment

1. **Setup Environment Variables in Vercel:**
   - Go to your Vercel project dashboard
   - Navigate to Settings → Environment Variables
   - Add all the required variables listed above

2. **Deploy:**
   ```bash
   # Deploy both frontend and backend together
   vercel --prod
   ```

3. **Configure Vercel Secrets (recommended for sensitive data):**
   ```bash
   vercel secrets add spotify-client-id "your_client_id"
   vercel secrets add spotify-client-secret "your_client_secret"
   vercel secrets add supabase-anon-key "your_supabase_key"
   # ... add all other secrets
   ```

### Option 2: Koyeb Deployment (Backend)

1. **Deploy Backend to Koyeb:**
   - Create a new Koyeb app
   - Connect your GitHub repository
   - Set the root directory to `bad-server`
   - Add all backend environment variables in the Koyeb dashboard

2. **Deploy Frontend to Vercel:**
   - Create a Vercel project for the frontend
   - Set the root directory to `bad-frontend`
   - Set `REACT_APP_API_URL` to your Koyeb backend URL
   - Add other frontend environment variables

### Option 3: Separate Deployments

**Backend (Koyeb/Railway/Render):**
1. Deploy `bad-server` folder to your preferred backend hosting
2. Set all backend environment variables
3. Note the deployed backend URL

**Frontend (Vercel/Netlify):**
1. Deploy `bad-frontend` folder to your preferred frontend hosting
2. Set `REACT_APP_API_URL` to your backend URL
3. Set other frontend environment variables

## Security Notes

1. **Never commit sensitive data:**
   - `.env` files are in `.gitignore`
   - `users.json`, `tokens.json` are in `.gitignore`
   - Use the `.env.example` files as templates

2. **Use strong passwords:**
   - Generate secure passwords for `ADMIN_PASSWORD` and `SPOTIFY_AUTH_PASSWORD`
   - Use different passwords for different environments

3. **Supabase Security:**
   - Use Row Level Security (RLS) policies
   - Never expose the service key in client-side code
   - The anon key is safe to use in frontend applications

## Getting Started

1. **Copy environment templates:**
   ```bash
   cp bad-server/.env.example bad-server/.env
   cp bad-frontend/.env.example bad-frontend/.env
   ```

2. **Fill in your values:**
   - Edit the `.env` files with your actual values
   - Get Spotify credentials from Spotify Developer Dashboard
   - Get Supabase credentials from your Supabase project

3. **Test locally:**
   ```bash
   # Backend
   cd bad-server
   npm install
   npm start

   # Frontend (in another terminal)
   cd bad-frontend
   npm install
   npm start
   ```

4. **Deploy to production:**
   - Follow one of the deployment options above
   - Set environment variables in your hosting platform
   - Test the deployed application

## Troubleshooting

- **Missing environment variables:** Check the server logs for validation errors
- **CORS issues:** Ensure `REACT_APP_API_URL` matches your backend domain
- **Spotify auth fails:** Verify `SPOTIFY_REDIRECT_URI` matches your backend URL + `/callback`
- **Database connection fails:** Check Supabase URL and key are correct
