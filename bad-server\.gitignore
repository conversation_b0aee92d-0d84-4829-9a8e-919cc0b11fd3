# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Sensitive data files
tokens.json
users.json
submissions.json
blocked.json

# Dependencies
node_modules/

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
