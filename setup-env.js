#!/usr/bin/env node

/**
 * Environment Setup Helper for BAD (Be A DJ)
 * 
 * This script helps you set up environment variables for local development
 * and provides guidance for production deployment.
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function setupEnvironment() {
  console.log('🎵 BAD (Be A DJ) - Environment Setup\n');
  
  console.log('This script will help you create .env files for local development.');
  console.log('For production deployment, set these variables in your hosting platform.\n');

  // Backend environment variables
  console.log('📡 Backend Configuration (bad-server/.env)');
  console.log('─'.repeat(50));
  
  const backendEnv = {};
  
  backendEnv.SPOTIFY_CLIENT_ID = await question('Spotify Client ID: ');
  backendEnv.SPOTIFY_CLIENT_SECRET = await question('Spotify Client Secret: ');
  backendEnv.SPOTIFY_PLAYLIST_ID = await question('Spotify Playlist ID: ');
  backendEnv.SPOTIFY_REDIRECT_URI = await question('Spotify Redirect URI (e.g., http://localhost:3000/callback): ');
  
  backendEnv.SUPABASE_URL = await question('Supabase URL: ');
  backendEnv.SUPABASE_ANON_KEY = await question('Supabase Anon Key: ');
  
  backendEnv.ADMIN_PASSWORD = await question('Admin Password (secure): ');
  backendEnv.SPOTIFY_AUTH_PASSWORD = await question('Spotify Auth Password (secure): ');
  
  backendEnv.PORT = await question('Port (default 3000): ') || '3000';

  // Frontend environment variables
  console.log('\n🎨 Frontend Configuration (bad-frontend/.env)');
  console.log('─'.repeat(50));
  
  const frontendEnv = {};
  
  frontendEnv.REACT_APP_API_URL = await question('Backend API URL (e.g., http://localhost:3000): ');
  frontendEnv.REACT_APP_LOGIN_PASSWORD = await question('Login Password: ');

  // Write backend .env file
  const backendEnvContent = Object.entries(backendEnv)
    .map(([key, value]) => `${key}=${value}`)
    .join('\n');
  
  fs.writeFileSync(path.join(__dirname, 'bad-server', '.env'), backendEnvContent);
  console.log('\n✅ Created bad-server/.env');

  // Write frontend .env file
  const frontendEnvContent = Object.entries(frontendEnv)
    .map(([key, value]) => `${key}=${value}`)
    .join('\n');
  
  fs.writeFileSync(path.join(__dirname, 'bad-frontend', '.env'), frontendEnvContent);
  console.log('✅ Created bad-frontend/.env');

  console.log('\n🚀 Setup complete!');
  console.log('\nNext steps:');
  console.log('1. cd bad-server && npm install && npm start');
  console.log('2. cd bad-frontend && npm install && npm start');
  console.log('\nFor production deployment, see DEPLOYMENT.md');

  rl.close();
}

if (require.main === module) {
  setupEnvironment().catch(console.error);
}

module.exports = { setupEnvironment };
